from fastapi import APIRouter, HTTPException
from app.services.agent_service import chat_with_agent_logic  # Import the service function
import logging
from fastapi import FastAPI, HTTPException, Form, File, UploadFile, Request
from pydantic import BaseModel
from typing import Optional, Dict, Any
import google.genai.types as types
import agent
from google.adk.runners import Runner
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.artifacts import InMemoryArtifactService
from dotenv import load_dotenv
from fastapi.responses import JSONResponse
import os

router = APIRouter()

@router.post("/agent/chat")
async def chat_with_agent(prompt: str, file: Optional[UploadFile] = None):
    try:
        # Call the business logic function from the service layer
        final_response_text, context = await chat_with_agent_logic(prompt, file)

        return {"status": "success", "response": final_response_text, "details": context}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Agent error: {str(e)}")
