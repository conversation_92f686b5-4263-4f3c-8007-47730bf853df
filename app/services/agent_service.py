from fastapi import HTTPException
import google.genai.types as types
from google.adk.runners import Runner
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.artifacts import InMemoryArtifactService
import logging
from app.services.agent import agent
from fastapi import UploadFile
from typing import Optional

# Initialize services
session_service = InMemorySessionService()
artifact_service = InMemoryArtifactService()

async def chat_with_agent_logic(prompt: str, file: Optional[UploadFile] = None):
    context = {}

    if not prompt:
        raise HTTPException(status_code=400, detail="Prompt is required")

    if file:
        if file.content_type != "application/pdf":
            raise HTTPException(status_code=400, detail="Only PDF files are allowed")
        file_bytes = await file.read()
        artifact_id = file.filename
        file_artifact = types.Part.from_bytes(data=file_bytes, mime_type=file.content_type)
        await artifact_service.save_artifact(
            app_name="tms_agent", user_id="user_1", session_id="session_001",
            filename=artifact_id, artifact=file_artifact,
        )
        context["artifact_id"] = artifact_id
        await artifact_service.load_artifact(app_name='tms_agent', user_id='user_1', session_id='session_001', filename=artifact_id)

    # Setup runner and agent interaction
    runner, user_id, session_id = await setup_runner(prompt)
    content = types.Content(role="user", parts=[types.Part(text=prompt)])
    
    final_response_text = "Agent did not produce a final response."
    
    async for event in runner.run_async(user_id=user_id, session_id=session_id, new_message=content):
        if event.is_final_response():
            if event.content and event.content.parts:
                final_response_text = event.content.parts[0].text
            break

    return final_response_text, context


async def setup_runner():
    APP_NAME = "tms_agent"
    USER_ID = "user_1"
    SESSION_ID = "session_001"

    session = await session_service.get_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)
    if session is None:
        session = await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID,
            state={"user:name": "Vandana Sharma", "task_status": "idle"}
        )

    runner = Runner(agent=agent.root_agent, app_name=APP_NAME, session_service=session_service, artifact_service=artifact_service)
    return runner, USER_ID, SESSION_ID
