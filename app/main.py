import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Form, File, UploadFile, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from app.api.v1 import endpoints
from app.services.agent_service import artifact_service, setup_runner
from app.models.agent_response import AgentR<PERSON>ponse
from typing import Optional
import google.genai.types as types

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()


# Initialize FastAPI app and services
app = FastAPI()

app.include_router(endpoints.router)


@app.get("/")
async def health_check():
    return {"status": "ok"}

@app.exception_handler(Exception)
async def universal_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}")
    return JSONResponse(status_code=500, content={"message": "Internal Server Error", "detail": str(exc)})
