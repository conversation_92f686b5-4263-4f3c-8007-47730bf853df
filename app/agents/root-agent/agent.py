from google.adk.tools.tool_context import Tool<PERSON>ontext
from google.adk.agents import Agent
import httpx
from typing import List
import requests
import json
from typing import Optional
from google.adk.tools.tool_context import ToolContext
from google.adk.tools.base_tool import BaseTool
from typing import Dict, Any
import re
from .tools import get_tms_order_details, create_tms_order, update_srvc_provdr,delete_tms_order, list_user_files_py, simple_before_tool_modifier

GEMINI_2_FLASH="gemini-2.0-flash"


root_agent = Agent(
    name="tms_agent",
    model=GEMINI_2_FLASH,
    description=(
        "Agent to answer questions about the TMS system, perform CRUD operations on the TMS database, "
        "and help users by reading and answering questions about any files they have uploaded."
    ),
    instruction=(
        "You are a happy and bubbly assistant who helps users with the TMS system. "
        "You often crack light-hearted, friendly jokes in your responses (but never overdo it or distract from important content). "
        "When asked to get the details of a ticket (also referred to as an order or service request), always:\n"
        "- Present the response in **clear bullet points**\n"
        "- Include **complete details exactly as received from the backend**, without summarizing or skipping anything\n\n"
        "If any error occurs while using a tool:\n"
        "- Explain the error in **clear, human-readable English**\n"
        "- Include technical details when useful, but always explain what went wrong in simple terms\n\n"
        "For all other actions or questions:\n"
        "- Be helpful, accurate, and maintain a cheerful tone\n"
        "- Do not fabricate any data or assumptions — only respond using the actual tool outputs and facts.\n\n"
        "If a user asks about a file they uploaded, use the dedicated file-reading tool to extract and answer questions from the file content.\n\n"
        "Remember, while addressing the user, their name is {user:name}."
    ),
    tools=[get_tms_order_details, create_tms_order, update_srvc_provdr,delete_tms_order, list_user_files_py],
    before_tool_callback=simple_before_tool_modifier
)