from google.adk.tools.tool_context import ToolContext
from google.adk.agents import Agent
import httpx
from typing import List
import requests
import json
from typing import Optional
from google.adk.tools.tool_context import ToolContext
from google.adk.tools.base_tool import BaseTool
from typing import Dict, Any
import re

GEMINI_2_FLASH="gemini-2.0-flash"


def get_tms_order_details(tms_order_ids: List[str], service_type_id: int) -> dict:
    """
    Retrieves order details from the TMS API.

    Args:
        tms_order_ids (List[str]): List of TMS order IDs to retrieve details for.
        service_type_id (int, optional): The service type ID.

    Returns:
        dict : on basis of success and error return a dict.

    """
    
    #url = "http://uat-tms.wify.co.in/v1/brands/order"
    url = "https://0351-2409-4056-207-5927-2d33-e566-4518-b796.ngrok-free.app/v1/brands/order"
    
    # Join list into comma-separated string for query param
    tms_order_ids_str = ",".join(tms_order_ids)
    
    params = {
        "service_type_id": service_type_id,
        "tms_order_ids": tms_order_ids_str
    }
    
    headers = {
        "Authorization": "Bearer F203A3AC-07F5-4D1E-8FDD-5119EE31268B",
        "Accept": "application/json",
        "User-Agent": "PostmanRuntime/7.28.0"
    }
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=10)
        print("response", response.json())
        response.raise_for_status()
        data = response.json()
        return {"status": "success", "report": data}
    except requests.HTTPError as e:
        return {"status": "error", "error_message": f"HTTP error: {e.response.status_code}"}
    except requests.RequestException as e:
        return {"status": "error", "error_message": str(e)}
    except Exception as e:
        return {"status": "error", "error_message": str(e)}

def create_tms_order(
    cust_mobile: str,
    cust_full_name: str,
    cust_pincode: str,
    # unique_order_id: str,
    request_description: str,
    service_type_id: str,
    request_priority: str = "Normal",
   
) -> dict:
    """
    Creates a new order in the TMS system.

    Args:
        cust_mobile (str): Customer's mobile number
        cust_full_name (str): Customer's full name
        cust_pincode (str,optional): Pincode
        request_description (str): Order description
        request_priority (str, optional): Priority. Defaults to "Normal".
        service_type_id (str): Service type ID.

    Returns:
        dict: Status and created order details or error message.
    """
    
    url = f"https://0351-2409-4056-207-5927-2d33-e566-4518-b796.ngrok-free.app/v1/brands/order/{service_type_id}"
    
    # Prepare request payload
    payload = {
        "batch_data": [
            {
                "cust_mobile": cust_mobile,
                "cust_full_name": cust_full_name,
                "cust_pincode": cust_pincode,
                "request_description": request_description,
                "request_priority": request_priority,
             
            }
        ]
    }
    
    headers = {
        "Authorization": "Bearer F203A3AC-07F5-4D1E-8FDD-5119EE31268B",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = httpx.post(url, json=payload, headers=headers, timeout=15.0)
        response.raise_for_status()
        
        return {
            "status": "success",
            "data": response.json()
        }
    except httpx.HTTPStatusError as e:
        error_detail = {}
        try:
            error_detail = e.response.json()
        except:
            error_detail = {"message": e.response.text}
            
        return {
            "status": "error",
            "error_message": f"HTTP error: {e.response.status_code}",
            "details": error_detail
        }
    except httpx.RequestError as e:
        return {
            "status": "error",
            "error_message": f"Request error: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Unexpected error: {str(e)}"
        }
		
# create_tms_order_tool = FunctionTool(func=create_tms_order)

def delete_tms_order(
    tms_display_code: str,
    srvc_type_id: int
) -> dict:
    """
    Delete an order in the TMS system.

    Args:
        tms_display_code (str): TMS display code
        srvc_type_id (int): Service type ID

    Returns:
        dict: Status and deleted order details or error message.
    """
    
    url = "https://0351-2409-4056-207-5927-2d33-e566-4518-b796.ngrok-free.app/v1/brands/order"
    
    payload = {
        "batch_data": [
            {
                "tms_display_code": tms_display_code,
                "srvc_type_id": srvc_type_id
            }
        ]
    }
    
    headers = {
        "Authorization": "Bearer F203A3AC-07F5-4D1E-8FDD-5119EE31268B",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        # ✅ Use requests.delete with data=json.dumps(...) to send body
        response = requests.delete(
            url,
            data=json.dumps(payload),
            headers=headers,
            timeout=15
        )
        response.raise_for_status()
        
        return {
            "status": "success",
            "data": response.json()
        }
    except requests.HTTPError as e:
        try:
            error_detail = response.json()
        except Exception:
            error_detail = {"message": response.text}
        
        return {
            "status": "error",
            "error_message": f"HTTP error: {response.status_code}",
            "details": error_detail
        }
    except requests.RequestException as e:
        return {
            "status": "error",
            "error_message": f"Request error: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Unexpected error: {str(e)}"
        }

def update_srvc_provdr(
        tms_display_code: str,
        srvc_type_id: int,
        service_provider_id: str
) -> dict:
    """
    Update the service provider for an order in the TMS system.

    Args:
        tms_display_code (str): TMS display code
        srvc_type_id (int): Service type ID
        service_provider_id (str): Service provider ID

    Returns:
        dict: Status and updated order details or error message.
    """
    url = "https://0351-2409-4056-207-5927-2d33-e566-4518-b796.ngrok-free.app/v1/brands/order/service_req"
    
    payload = {
        "batch_data": [
            {
                "tms_display_code": tms_display_code,
                "srvc_type_id": srvc_type_id,
                "service_provider_id": service_provider_id
            }
        ]
    }
    
    headers = {
        "Authorization": "Bearer F203A3AC-07F5-4D1E-8FDD-5119EE31268B",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    try:
        response = requests.put(
            url,
            json=payload,
            headers=headers,
            timeout=15
        )
        response.raise_for_status()
        
        return {
            "status": "success",
            "data": response.json()
        }
    except requests.HTTPError as e:
        try:
            error_detail = response.json()
        except Exception:
            error_detail = {"message": response.text}
        
        return {
            "status": "error",
            "error_message": f"HTTP error: {response.status_code}",
            "details": error_detail
        }
    except requests.RequestException as e:
        return {
            "status": "error",
            "error_message": f"Request error: {str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Unexpected error: {str(e)}"
        }

def simple_before_tool_modifier(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext
) -> Optional[Dict]:
    """Inspects/modifies tool args or skips the tool call."""
    agent_name = tool_context.agent_name
    tool_name = tool.name
    print(f"[Callback] Before tool call for tool '{tool_name}' in agent '{agent_name}'")
    print(f"[Callback] Original args: {args}")


    if tool_name == 'create_tms_order':
     
     # Validate 'request_priority' (must be one of the allowed values)
     allowed_priorities = ["High", "Normal", "Urgent", "Low"]
     priority = args.get("request_priority", "Normal")  # Default to 'Normal' if not provided
     if priority not in allowed_priorities:
        error_message = f"Invalid 'request_priority'. Must be one of {allowed_priorities}. Provided: {priority}"
        print(f"[Callback] Error: {error_message}")
        return {
                "status": "error",
                "error_message": error_message
            }

     # Validate phone number 'cust_mobile' (Indian or Bangladeshi number)
     phone_number = args.get("cust_mobile")
     if phone_number:
        # Indian: 10 digits, Bangladeshi: 11 digits
        if not re.match(r"^(\d{10}|\d{11})$", phone_number):
            error_message = f"Invalid phone number. Must be a 10-digit Indian number or 11-digit Bangladeshi number. Provided: {phone_number}"
            print(f"[Callback] Error: {error_message}")
            return {
                "status": "error",
                "error_message": error_message
            }

     # For other tools, no validation, just return the input
    return None

async def list_user_files_py(tool_context: ToolContext) -> str:
    """Tool to list available artifacts for the user."""
    try:
        available_files = await tool_context.list_artifacts()
        print("available_files",available_files)
        if not available_files:
            return "You have no saved artifacts."
        else:
            # Format the list for the user/LLM
            file_list_str = "\n".join([f"- {fname}" for fname in available_files])
            return f"Here are your available Python artifacts:\n{file_list_str}"
    except ValueError as e:
        print(f"Error listing Python artifacts: {e}. Is ArtifactService configured?")
        return "Error: Could not list Python artifacts."
    except Exception as e:
        print(f"An unexpected error occurred during Python artifact list: {e}")
        return "Error: An unexpected error occurred while listing Python artifacts."
