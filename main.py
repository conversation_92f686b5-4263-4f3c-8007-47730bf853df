import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Form, File, UploadFile, Request
from pydantic import BaseModel
from typing import Optional, Dict, Any
import google.genai.types as types
import agent
from google.adk.runners import Runner
from google.adk.sessions.in_memory_session_service import InMemorySessionService
from google.adk.artifacts import InMemoryArtifactService
from dotenv import load_dotenv
from fastapi.responses import JSONResponse
import os

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()
SECRET_KEY = os.getenv("SECRET_KEY")
DATABASE_URL = os.getenv("DATABASE_URL")

# Initialize FastAPI app and services
app = FastAPI()
session_service = InMemorySessionService()
artifact_service = InMemoryArtifactService()


class AgentRequest(BaseModel):
    prompt: str
    context: Optional[Dict[str, Any]] = None


class AgentResponse(BaseModel):
    status: str
    response: str
    details: Optional[Dict[str, Any]] = None

@app.get("/")
async def health_check():
    return {"status": "ok"}

@app.post("/agent/chat", response_model=AgentResponse)
async def chat_with_agent(
   prompt: str = Form(...),
   file: Optional[UploadFile] = File(None)
):

    try:
        context = {}

        # Validate prompt
        if not prompt:
            raise HTTPException(status_code=400, detail="Prompt is required")

        # Handle optional PDF file
        if file:
            if file.content_type != "application/pdf":
                raise HTTPException(status_code=400, detail="Only PDF files are allowed")
            file_bytes = await file.read()
            artifact_id = file.filename
            file_artifact = types.Part.from_bytes(data=file_bytes, mime_type=file.content_type)
            # Save the artifact
            await artifact_service.save_artifact(
                app_name="tms_agent", user_id="user_1", session_id="session_001",
                filename=artifact_id, artifact=file_artifact,
            )
            context["artifact_id"] = artifact_id
            await artifact_service.load_artifact(app_name='tms_agent', user_id='user_1', session_id='session_001', filename=artifact_id)

        # Step 2: Setup Runner
        runner, user_id, session_id = await setup_runner(prompt)

        content = types.Content(role="user", parts=[types.Part(text=prompt)])

        final_response_text = "Agent did not produce a final response."

        async for event in runner.run_async(user_id=user_id, session_id=session_id, new_message=content):
            if event.is_final_response():
                if event.content and event.content.parts:
                    final_response_text = event.content.parts[0].text
                break

        return AgentResponse(status="success", response=final_response_text, details=context)

    except Exception as e:
        logger.error(f"Agent error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Agent error: {str(e)}")


@app.exception_handler(Exception)
async def universal_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unexpected error: {str(exc)}")
    return JSONResponse(status_code=500, content={"message": "Internal Server Error", "detail": str(exc)})


async def setup_runner():
    APP_NAME = "tms_agent"
    USER_ID = "user_1"
    SESSION_ID = "session_001"

    session = await session_service.get_session(app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID)
    if session is None:
        session = await session_service.create_session(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID,
            state={"user:name": "Vandana Sharma", "task_status": "idle"}
        )

    runner = Runner(agent=agent.root_agent, app_name=APP_NAME, session_service=session_service, artifact_service=artifact_service)
    return runner, USER_ID, SESSION_ID

